"""
简单的FastMCP测试用例
@author: FangGL
@date: 2025-01-08
"""

import asyncio

from fastmcp import Client
from simple_server import mcp


def mcp_server():
    """测试MCP服务器功能"""
    print("开始测试FastMCP服务器...")
    
    # 创建客户端连接到服务器
    client = Client(mcp)
    
    async with client:
        # 测试加法工具
        print("\n--- 测试工具 ---")
        result =  client.call_tool("add", {"a": 5, "b": 3})
        print(f"5 + 3 = {result.content[0].text}")
        
        # 测试乘法工具
        result =  client.call_tool("multiply", {"a": 4, "b": 6})
        print(f"4 × 6 = {result.content[0].text}")
        
        # 测试问候工具
        result =  client.call_tool("greet", {"name": "张三"})
        print(f"问候结果: {result.content[0].text}")
        
        # 测试资源读取
        print("\n--- 测试资源 ---")
        result =  client.read_resource("config://demo")
        print(f"配置信息: {result[0].text}")

        # 测试提示
        print("\n--- 测试提示 ---")
        result = client.get_prompt("help_prompt", {})
        print(f"帮助信息: {result.messages[0].content.text}")
        
        # 列出可用工具
        print("\n--- 可用工具列表 ---")
        tools =  client.list_tools()
        for tool in tools:
            print(f"- {tool.name}: {tool.description}")
        
        print("\n✅ 测试完成！")

if __name__ == "__main__":
    # 运行测试
    mcp_server()