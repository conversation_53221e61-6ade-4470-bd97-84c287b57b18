"""
简单的FastMCP测试服务器
@author: FangGL
@date: 2025-01-08
"""

from fastmcp import FastMCP

# 创建MCP服务器实例
mcp = FastMCP("Demo 🚀")

@mcp.tool()
def add(a: int, b: int) -> int:
    """将两个数字相加"""
    return a + b

@mcp.tool()
def multiply(a: int, b: int) -> int:
    """将两个数字相乘"""
    return a * b

@mcp.tool()
def greet(name: str) -> str:
    """问候用户"""
    return f"你好, {name}!"

@mcp.resource("config://demo")
def get_demo_config():
    """获取演示配置"""
    return {
        "name": "FastMCP Demo",
        "version": "1.0",
        "author": "fastmcp"
    }

@mcp.prompt()
def help_prompt() -> str:
    """帮助提示"""
    return "我是FastMCP演示服务器，可以帮您进行简单的数学计算和问候。"

if __name__ == "__main__":
    print("启动简单FastMCP服务器...")
    mcp.run() 